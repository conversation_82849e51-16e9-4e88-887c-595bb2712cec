/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: 6P769Hd_cTVa

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import SubcomponentDropdownSelector from "../../SubcomponentDropdownSelector"; // plasmic-import: eb9GZKRV2I9e/component
import SubcomponentTextInput from "../../SubcomponentTextInput"; // plasmic-import: Pt5FPuEzirSe/component
import SubcomponentIconWithText from "../../SubcomponentIconWithText"; // plasmic-import: _eFlbiSm6hZU/component
import SubcomponentCheckbox from "../../SubcomponentCheckbox"; // plasmic-import: OeFTSKgMult8/component
import SubcomponentDeleteButton from "../../SubcomponentDeleteButton"; // plasmic-import: WMfLdq1qoZyo/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileTileEducationTile.module.css"; // plasmic-import: 6P769Hd_cTVa/css

import GradCapIcon from "./icons/PlasmicIcon__GradCap"; // plasmic-import: i9bbaFOlpQVC/icon
import MapPinIcon from "./icons/PlasmicIcon__MapPin"; // plasmic-import: Q_wDLzMm5l2Y/icon
import MapIcon from "./icons/PlasmicIcon__Map"; // plasmic-import: 2X2dwcZjjUKR/icon
import CalendarIcon from "./icons/PlasmicIcon__Calendar"; // plasmic-import: -xP51745fXLc/icon

createPlasmicElementProxy;

export type PlasmicProfileTileEducationTile__VariantMembers = {
  editable: "editable";
  overview: "overview";
  test: "test";
};
export type PlasmicProfileTileEducationTile__VariantsArgs = {
  editable?: SingleBooleanChoiceArg<"editable">;
  overview?: SingleBooleanChoiceArg<"overview">;
  test?: SingleBooleanChoiceArg<"test">;
};
type VariantPropType = keyof PlasmicProfileTileEducationTile__VariantsArgs;
export const PlasmicProfileTileEducationTile__VariantProps =
  new Array<VariantPropType>("editable", "overview", "test");

export type PlasmicProfileTileEducationTile__ArgsType = {
  educationId?: string;
  degreeNameInputValue?: string;
  educationalInstitutionInputValue?: string;
  locationInputValue?: string;
  startDateInputValue?: string;
  endDateInputValue?: string;
  graduatedCheckboxValue?: boolean;
  onDegreeNameInputValueChange?: (val: string) => void;
  onEducationalInstitutionInputValueChange?: (val: string) => void;
  onLocationInputValueChange?: (val: string) => void;
  onStartDateInputValueChange?: (val: string) => void;
  onEndDateInputValueChange?: (val: string) => void;
  onGraduatedCheckboxValueChange?: (val: boolean) => void;
  deleteButtonOnClick?: (event: any) => void;
  deleteButtonDisabled?: any;
  deleteButtonClickStage?: number;
  onDeleteButtonDisabledChange?: (val: any) => void;
  onDeleteButtonClickStageChange?: (val: number) => void;
};
type ArgPropType = keyof PlasmicProfileTileEducationTile__ArgsType;
export const PlasmicProfileTileEducationTile__ArgProps = new Array<ArgPropType>(
  "educationId",
  "degreeNameInputValue",
  "educationalInstitutionInputValue",
  "locationInputValue",
  "startDateInputValue",
  "endDateInputValue",
  "graduatedCheckboxValue",
  "onDegreeNameInputValueChange",
  "onEducationalInstitutionInputValueChange",
  "onLocationInputValueChange",
  "onStartDateInputValueChange",
  "onEndDateInputValueChange",
  "onGraduatedCheckboxValueChange",
  "deleteButtonOnClick",
  "deleteButtonDisabled",
  "deleteButtonClickStage",
  "onDeleteButtonDisabledChange",
  "onDeleteButtonClickStageChange"
);

export type PlasmicProfileTileEducationTile__OverridesType = {
  educationSpacingContainer?: Flex__<"div">;
  schoolIconDisplay?: Flex__<"svg">;
  inforationFormatting?: Flex__<"div">;
  titleContainer?: Flex__<"div">;
  degreeNameContainer?: Flex__<"div">;
  subcomponentDropdownSelector?: Flex__<typeof SubcomponentDropdownSelector>;
  degreeNameInputAndFullDisplay?: Flex__<typeof SubcomponentTextInput>;
  educationalInstitutionInput?: Flex__<typeof SubcomponentTextInput>;
  locationInput?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot3?: Flex__<"svg">;
  eduInfoBar?: Flex__<"div">;
  graduationDatesDisplay?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot4?: Flex__<"svg">;
  startDateInput?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot5?: Flex__<"svg">;
  endDateInput?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot6?: Flex__<"svg">;
  graduatedInput?: Flex__<typeof SubcomponentCheckbox>;
  subDeleteButton?: Flex__<typeof SubcomponentDeleteButton>;
};

export interface DefaultProfileTileEducationTileProps {
  educationId?: string;
  degreeNameInputValue?: string;
  educationalInstitutionInputValue?: string;
  locationInputValue?: string;
  startDateInputValue?: string;
  endDateInputValue?: string;
  graduatedCheckboxValue?: boolean;
  onDegreeNameInputValueChange?: (val: string) => void;
  onEducationalInstitutionInputValueChange?: (val: string) => void;
  onLocationInputValueChange?: (val: string) => void;
  onStartDateInputValueChange?: (val: string) => void;
  onEndDateInputValueChange?: (val: string) => void;
  onGraduatedCheckboxValueChange?: (val: boolean) => void;
  deleteButtonOnClick?: (event: any) => void;
  deleteButtonDisabled?: any;
  deleteButtonClickStage?: number;
  onDeleteButtonDisabledChange?: (val: any) => void;
  onDeleteButtonClickStageChange?: (val: number) => void;
  editable?: SingleBooleanChoiceArg<"editable">;
  overview?: SingleBooleanChoiceArg<"overview">;
  test?: SingleBooleanChoiceArg<"test">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileTileEducationTile__RenderFunc(props: {
  variants: PlasmicProfileTileEducationTile__VariantsArgs;
  args: PlasmicProfileTileEducationTile__ArgsType;
  overrides: PlasmicProfileTileEducationTile__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "editable",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.editable
      },
      {
        path: "degreeNameInputAndFullDisplay.value",
        type: "writable",
        variableType: "text",

        valueProp: "degreeNameInputValue",
        onChangeProp: "onDegreeNameInputValueChange"
      },
      {
        path: "locationInput.inputValue",
        type: "writable",
        variableType: "text",

        valueProp: "locationInputValue",
        onChangeProp: "onLocationInputValueChange"
      },
      {
        path: "graduationDatesDisplay.inputValue",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "startDateInput.inputValue",
        type: "writable",
        variableType: "text",

        valueProp: "startDateInputValue",
        onChangeProp: "onStartDateInputValueChange"
      },
      {
        path: "endDateInput.inputValue",
        type: "writable",
        variableType: "text",

        valueProp: "endDateInputValue",
        onChangeProp: "onEndDateInputValueChange"
      },
      {
        path: "graduatedInput.isChecked",
        type: "writable",
        variableType: "boolean",

        valueProp: "graduatedCheckboxValue",
        onChangeProp: "onGraduatedCheckboxValueChange"
      },
      {
        path: "overview",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.overview
      },
      {
        path: "subDeleteButton.clickStage",
        type: "writable",
        variableType: "number",

        valueProp: "deleteButtonClickStage",
        onChangeProp: "onDeleteButtonClickStageChange"
      },
      {
        path: "subDeleteButton.disabled",
        type: "writable",
        variableType: "text",

        valueProp: "deleteButtonDisabled",
        onChangeProp: "onDeleteButtonDisabledChange"
      },
      {
        path: "pendingUpdates",
        type: "private",
        variableType: "boolean",
        initFunc: ({ $props, $state, $queries, $ctx }) => false
      },
      {
        path: "educationalInstitutionInput.value",
        type: "writable",
        variableType: "text",

        valueProp: "educationalInstitutionInputValue",
        onChangeProp: "onEducationalInstitutionInputValueChange"
      },
      {
        path: "degreeNameInputAndFullDisplay.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "educationalInstitutionInput.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "test",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.test
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"educationSpacingContainer"}
      data-plasmic-override={overrides.educationSpacingContainer}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.educationSpacingContainer,
        {
          [sty.educationSpacingContainereditable]: hasVariant(
            $state,
            "editable",
            "editable"
          ),
          [sty.educationSpacingContaineroverview]: hasVariant(
            $state,
            "overview",
            "overview"
          ),
          [sty.educationSpacingContainertest]: hasVariant(
            $state,
            "test",
            "test"
          )
        }
      )}
    >
      <GradCapIcon
        data-plasmic-name={"schoolIconDisplay"}
        data-plasmic-override={overrides.schoolIconDisplay}
        className={classNames(projectcss.all, sty.schoolIconDisplay, {
          [sty.schoolIconDisplayeditable]: hasVariant(
            $state,
            "editable",
            "editable"
          ),
          [sty.schoolIconDisplayoverview]: hasVariant(
            $state,
            "overview",
            "overview"
          )
        })}
        role={"img"}
      />

      <div
        data-plasmic-name={"inforationFormatting"}
        data-plasmic-override={overrides.inforationFormatting}
        className={classNames(projectcss.all, sty.inforationFormatting, {
          [sty.inforationFormattingeditable]: hasVariant(
            $state,
            "editable",
            "editable"
          ),
          [sty.inforationFormattingoverview]: hasVariant(
            $state,
            "overview",
            "overview"
          ),
          [sty.inforationFormattingtest]: hasVariant($state, "test", "test")
        })}
      >
        <div
          data-plasmic-name={"titleContainer"}
          data-plasmic-override={overrides.titleContainer}
          className={classNames(projectcss.all, sty.titleContainer, {
            [sty.titleContainereditable]: hasVariant(
              $state,
              "editable",
              "editable"
            ),
            [sty.titleContaineroverview]: hasVariant(
              $state,
              "overview",
              "overview"
            )
          })}
        >
          <Stack__
            as={"div"}
            data-plasmic-name={"degreeNameContainer"}
            data-plasmic-override={overrides.degreeNameContainer}
            hasGap={true}
            className={classNames(projectcss.all, sty.degreeNameContainer, {
              [sty.degreeNameContainereditable]: hasVariant(
                $state,
                "editable",
                "editable"
              ),
              [sty.degreeNameContaineroverview]: hasVariant(
                $state,
                "overview",
                "overview"
              )
            })}
          >
            {(hasVariant($state, "editable", "editable") ? true : false) ? (
              <SubcomponentDropdownSelector
                data-plasmic-name={"subcomponentDropdownSelector"}
                data-plasmic-override={overrides.subcomponentDropdownSelector}
                className={classNames(
                  "__wab_instance",
                  sty.subcomponentDropdownSelector,
                  {
                    [sty.subcomponentDropdownSelectoreditable]: hasVariant(
                      $state,
                      "editable",
                      "editable"
                    ),
                    [sty.subcomponentDropdownSelectoroverview]: hasVariant(
                      $state,
                      "overview",
                      "overview"
                    )
                  }
                )}
              />
            ) : null}
            {(
              hasVariant($state, "editable", "editable")
                ? (() => {
                    try {
                      return undefined;
                    } catch (e) {
                      if (
                        e instanceof TypeError ||
                        e?.plasmicType === "PlasmicUndefinedDataError"
                      ) {
                        return true;
                      }
                      throw e;
                    }
                  })()
                : true
            ) ? (
              <SubcomponentTextInput
                data-plasmic-name={"degreeNameInputAndFullDisplay"}
                data-plasmic-override={overrides.degreeNameInputAndFullDisplay}
                className={classNames(
                  "__wab_instance",
                  sty.degreeNameInputAndFullDisplay,
                  {
                    [sty.degreeNameInputAndFullDisplayeditable]: hasVariant(
                      $state,
                      "editable",
                      "editable"
                    ),
                    [sty.degreeNameInputAndFullDisplayoverview]: hasVariant(
                      $state,
                      "overview",
                      "overview"
                    )
                  }
                )}
                editView={"heading3"}
                errorMessage={generateStateValueProp($state, [
                  "degreeNameInputAndFullDisplay",
                  "errorMessage"
                ])}
                inputHoverText={"Degree Name"}
                inputName={"Degree Name"}
                inputPlaceholder={"Degree Name"}
                inputValue={generateStateValueProp($state, [
                  "degreeNameInputAndFullDisplay",
                  "value"
                ])}
                onErrorMessageChange={async (...eventArgs: any) => {
                  generateStateOnChangeProp($state, [
                    "degreeNameInputAndFullDisplay",
                    "errorMessage"
                  ]).apply(null, eventArgs);

                  if (
                    eventArgs.length > 1 &&
                    eventArgs[1] &&
                    eventArgs[1]._plasmic_state_init_
                  ) {
                    return;
                  }
                }}
                onInputValueChange={async (...eventArgs: any) => {
                  generateStateOnChangeProp($state, [
                    "degreeNameInputAndFullDisplay",
                    "value"
                  ]).apply(null, eventArgs);

                  if (
                    eventArgs.length > 1 &&
                    eventArgs[1] &&
                    eventArgs[1]._plasmic_state_init_
                  ) {
                    return;
                  }
                }}
                viewOnly={
                  hasVariant($state, "editable", "editable") ? undefined : true
                }
              />
            ) : null}
          </Stack__>
          <SubcomponentTextInput
            data-plasmic-name={"educationalInstitutionInput"}
            data-plasmic-override={overrides.educationalInstitutionInput}
            className={classNames(
              "__wab_instance",
              sty.educationalInstitutionInput,
              {
                [sty.educationalInstitutionInputeditable]: hasVariant(
                  $state,
                  "editable",
                  "editable"
                ),
                [sty.educationalInstitutionInputoverview]: hasVariant(
                  $state,
                  "overview",
                  "overview"
                )
              }
            )}
            displayText={(() => {
              try {
                return $state.educationalInstitutionInput.value;
              } catch (e) {
                if (
                  e instanceof TypeError ||
                  e?.plasmicType === "PlasmicUndefinedDataError"
                ) {
                  return undefined;
                }
                throw e;
              }
            })()}
            editView={"subHeading"}
            errorMessage={generateStateValueProp($state, [
              "educationalInstitutionInput",
              "errorMessage"
            ])}
            fieldNameRemainVisible={true}
            inputHoverText={"Institution"}
            inputName={"Educational Institution"}
            inputNameAsPlaceholder={false}
            inputPlaceholder={"Ex: Massachusetts Institute of Technology"}
            inputValue={generateStateValueProp($state, [
              "educationalInstitutionInput",
              "value"
            ])}
            onErrorMessageChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "educationalInstitutionInput",
                "errorMessage"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onInputValueChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "educationalInstitutionInput",
                "value"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            viewOnly={
              hasVariant($state, "editable", "editable") ? undefined : true
            }
          />

          {(
            hasVariant($state, "editable", "editable")
              ? true
              : (() => {
                  try {
                    return !!$state.locationInput.inputValue;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return false;
                    }
                    throw e;
                  }
                })()
          ) ? (
            <SubcomponentIconWithText
              data-plasmic-name={"locationInput"}
              data-plasmic-override={overrides.locationInput}
              className={classNames("__wab_instance", sty.locationInput, {
                [sty.locationInputeditable]: hasVariant(
                  $state,
                  "editable",
                  "editable"
                ),
                [sty.locationInputoverview]: hasVariant(
                  $state,
                  "overview",
                  "overview"
                )
              })}
              displayText={(() => {
                try {
                  return $state.locationInput.inputValue;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })()}
              editable={
                hasVariant($state, "editable", "editable")
                  ? "editableText"
                  : undefined
              }
              fieldNameRemainVisible={true}
              iconSpot2={
                <PlasmicIcon__
                  data-plasmic-name={"iconSpot3"}
                  data-plasmic-override={overrides.iconSpot3}
                  PlasmicIconType={
                    hasVariant($state, "editable", "editable")
                      ? MapIcon
                      : MapPinIcon
                  }
                  className={classNames(projectcss.all, sty.iconSpot3, {
                    [sty.iconSpot3editable]: hasVariant(
                      $state,
                      "editable",
                      "editable"
                    )
                  })}
                  role={"img"}
                />
              }
              inputHoverText={"Location of Institution"}
              inputName={"Location of Institution"}
              inputPlaceholder={"Ex: Chicago, IL"}
              inputValue={generateStateValueProp($state, [
                "locationInput",
                "inputValue"
              ])}
              onInputValueChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "locationInput",
                  "inputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              withoutIcon={
                hasVariant($state, "editable", "editable") ? true : undefined
              }
            />
          ) : null}
        </div>
        <Stack__
          as={"div"}
          data-plasmic-name={"eduInfoBar"}
          data-plasmic-override={overrides.eduInfoBar}
          hasGap={true}
          className={classNames(projectcss.all, sty.eduInfoBar, {
            [sty.eduInfoBareditable]: hasVariant(
              $state,
              "editable",
              "editable"
            ),
            [sty.eduInfoBaroverview]: hasVariant($state, "overview", "overview")
          })}
        >
          {(
            hasVariant($state, "editable", "editable")
              ? true
              : (() => {
                  try {
                    return (() => {
                      if (
                        !$state.startDateInput.inputValue ||
                        !$state.endDateInput.inputValue
                      ) {
                        return false;
                      } else {
                        return true;
                      }
                    })();
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return false;
                    }
                    throw e;
                  }
                })()
          ) ? (
            <SubcomponentIconWithText
              data-plasmic-name={"graduationDatesDisplay"}
              data-plasmic-override={overrides.graduationDatesDisplay}
              className={classNames(
                "__wab_instance",
                sty.graduationDatesDisplay,
                {
                  [sty.graduationDatesDisplayeditable]: hasVariant(
                    $state,
                    "editable",
                    "editable"
                  ),
                  [sty.graduationDatesDisplayoverview]: hasVariant(
                    $state,
                    "overview",
                    "overview"
                  )
                }
              )}
              displayText={(() => {
                try {
                  return (() => {
                    if (
                      !$state.startDateInput.inputValue ||
                      !$state.endDateInput.inputValue
                    ) {
                      return null;
                    } else {
                      const startDate = new Date(
                        $state.startDateInput.inputValue
                          .split("T")[0]
                          .split("-")
                          .join("/")
                      );
                      const endDate = new Date(
                        $state.endDateInput.inputValue
                          .split("T")[0]
                          .split("-")
                          .join("/")
                      );
                      const formattedStartDate = `${startDate.toLocaleString(
                        "default",
                        { month: "long" }
                      )}, ${startDate.getFullYear()}`;
                      const formattedEndDate = `${endDate.toLocaleString(
                        "default",
                        { month: "long" }
                      )}, ${endDate.getFullYear()}`;
                      return `${formattedStartDate} - ${formattedEndDate}`;
                    }
                  })();
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })()}
              iconSpot2={
                <CalendarIcon
                  data-plasmic-name={"iconSpot4"}
                  data-plasmic-override={overrides.iconSpot4}
                  className={classNames(projectcss.all, sty.iconSpot4)}
                  role={"img"}
                />
              }
              inputHoverText={"Display Dates"}
              inputName={"Display Dates"}
              inputPlaceholder={"Display Dates"}
              inputValue={generateStateValueProp($state, [
                "graduationDatesDisplay",
                "inputValue"
              ])}
              onInputValueChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "graduationDatesDisplay",
                  "inputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
            />
          ) : null}
          <SubcomponentIconWithText
            data-plasmic-name={"startDateInput"}
            data-plasmic-override={overrides.startDateInput}
            className={classNames("__wab_instance", sty.startDateInput, {
              [sty.startDateInputeditable]: hasVariant(
                $state,
                "editable",
                "editable"
              ),
              [sty.startDateInputoverview]: hasVariant(
                $state,
                "overview",
                "overview"
              )
            })}
            displayText={(() => {
              try {
                return $state.startDateInput.inputValue;
              } catch (e) {
                if (
                  e instanceof TypeError ||
                  e?.plasmicType === "PlasmicUndefinedDataError"
                ) {
                  return undefined;
                }
                throw e;
              }
            })()}
            editable={
              hasVariant($state, "editable", "editable")
                ? "editableText"
                : undefined
            }
            iconSpot2={
              <CalendarIcon
                data-plasmic-name={"iconSpot5"}
                data-plasmic-override={overrides.iconSpot5}
                className={classNames(projectcss.all, sty.iconSpot5)}
                role={"img"}
              />
            }
            inputHoverText={"Start Date"}
            inputName={"Start Date"}
            inputType={
              hasVariant($state, "editable", "editable")
                ? (() => {
                    try {
                      return "date";
                    } catch (e) {
                      if (
                        e instanceof TypeError ||
                        e?.plasmicType === "PlasmicUndefinedDataError"
                      ) {
                        return "date";
                      }
                      throw e;
                    }
                  })()
                : (() => {
                    try {
                      return "month";
                    } catch (e) {
                      if (
                        e instanceof TypeError ||
                        e?.plasmicType === "PlasmicUndefinedDataError"
                      ) {
                        return "date";
                      }
                      throw e;
                    }
                  })()
            }
            inputValue={generateStateValueProp($state, [
              "startDateInput",
              "inputValue"
            ])}
            onInputValueChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "startDateInput",
                "inputValue"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            withoutIcon={
              hasVariant($state, "editable", "editable") ? true : true
            }
          />

          <SubcomponentIconWithText
            data-plasmic-name={"endDateInput"}
            data-plasmic-override={overrides.endDateInput}
            className={classNames("__wab_instance", sty.endDateInput, {
              [sty.endDateInputeditable]: hasVariant(
                $state,
                "editable",
                "editable"
              ),
              [sty.endDateInputoverview]: hasVariant(
                $state,
                "overview",
                "overview"
              )
            })}
            displayText={(() => {
              try {
                return $state.endDateInput.inputValue;
              } catch (e) {
                if (
                  e instanceof TypeError ||
                  e?.plasmicType === "PlasmicUndefinedDataError"
                ) {
                  return undefined;
                }
                throw e;
              }
            })()}
            editable={"editableText"}
            iconSpot2={
              <svg
                data-plasmic-name={"iconSpot6"}
                data-plasmic-override={overrides.iconSpot6}
                className={classNames(projectcss.all, sty.iconSpot6)}
                role={"img"}
              />
            }
            inputHoverText={"End Date"}
            inputName={"End Date"}
            inputType={(() => {
              try {
                return "date";
              } catch (e) {
                if (
                  e instanceof TypeError ||
                  e?.plasmicType === "PlasmicUndefinedDataError"
                ) {
                  return "date";
                }
                throw e;
              }
            })()}
            inputValue={generateStateValueProp($state, [
              "endDateInput",
              "inputValue"
            ])}
            onInputValueChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "endDateInput",
                "inputValue"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            withoutIcon={true}
          />

          <SubcomponentCheckbox
            data-plasmic-name={"graduatedInput"}
            data-plasmic-override={overrides.graduatedInput}
            className={classNames("__wab_instance", sty.graduatedInput, {
              [sty.graduatedInputeditable]: hasVariant(
                $state,
                "editable",
                "editable"
              ),
              [sty.graduatedInputoverview]: hasVariant(
                $state,
                "overview",
                "overview"
              )
            })}
            isChecked={
              generateStateValueProp($state, ["graduatedInput", "isChecked"]) ??
              false
            }
            name={"Graduated Status"}
            onChange={async (...eventArgs: any) => {
              ((...eventArgs) => {
                generateStateOnChangeProp($state, [
                  "graduatedInput",
                  "isChecked"
                ])(eventArgs[0]);
              }).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
          >
            {"Graduated"}
          </SubcomponentCheckbox>
        </Stack__>
      </div>
      <SubcomponentDeleteButton
        data-plasmic-name={"subDeleteButton"}
        data-plasmic-override={overrides.subDeleteButton}
        className={classNames("__wab_instance", sty.subDeleteButton, {
          [sty.subDeleteButtoneditable]: hasVariant(
            $state,
            "editable",
            "editable"
          )
        })}
        onClick={args.deleteButtonOnClick}
        onClickStageChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "subDeleteButton",
            "clickStage"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onDisabledChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "subDeleteButton",
            "disabled"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
      />
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  educationSpacingContainer: [
    "educationSpacingContainer",
    "schoolIconDisplay",
    "inforationFormatting",
    "titleContainer",
    "degreeNameContainer",
    "subcomponentDropdownSelector",
    "degreeNameInputAndFullDisplay",
    "educationalInstitutionInput",
    "locationInput",
    "iconSpot3",
    "eduInfoBar",
    "graduationDatesDisplay",
    "iconSpot4",
    "startDateInput",
    "iconSpot5",
    "endDateInput",
    "iconSpot6",
    "graduatedInput",
    "subDeleteButton"
  ],
  schoolIconDisplay: ["schoolIconDisplay"],
  inforationFormatting: [
    "inforationFormatting",
    "titleContainer",
    "degreeNameContainer",
    "subcomponentDropdownSelector",
    "degreeNameInputAndFullDisplay",
    "educationalInstitutionInput",
    "locationInput",
    "iconSpot3",
    "eduInfoBar",
    "graduationDatesDisplay",
    "iconSpot4",
    "startDateInput",
    "iconSpot5",
    "endDateInput",
    "iconSpot6",
    "graduatedInput"
  ],
  titleContainer: [
    "titleContainer",
    "degreeNameContainer",
    "subcomponentDropdownSelector",
    "degreeNameInputAndFullDisplay",
    "educationalInstitutionInput",
    "locationInput",
    "iconSpot3"
  ],
  degreeNameContainer: [
    "degreeNameContainer",
    "subcomponentDropdownSelector",
    "degreeNameInputAndFullDisplay"
  ],
  subcomponentDropdownSelector: ["subcomponentDropdownSelector"],
  degreeNameInputAndFullDisplay: ["degreeNameInputAndFullDisplay"],
  educationalInstitutionInput: ["educationalInstitutionInput"],
  locationInput: ["locationInput", "iconSpot3"],
  iconSpot3: ["iconSpot3"],
  eduInfoBar: [
    "eduInfoBar",
    "graduationDatesDisplay",
    "iconSpot4",
    "startDateInput",
    "iconSpot5",
    "endDateInput",
    "iconSpot6",
    "graduatedInput"
  ],
  graduationDatesDisplay: ["graduationDatesDisplay", "iconSpot4"],
  iconSpot4: ["iconSpot4"],
  startDateInput: ["startDateInput", "iconSpot5"],
  iconSpot5: ["iconSpot5"],
  endDateInput: ["endDateInput", "iconSpot6"],
  iconSpot6: ["iconSpot6"],
  graduatedInput: ["graduatedInput"],
  subDeleteButton: ["subDeleteButton"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  educationSpacingContainer: "div";
  schoolIconDisplay: "svg";
  inforationFormatting: "div";
  titleContainer: "div";
  degreeNameContainer: "div";
  subcomponentDropdownSelector: typeof SubcomponentDropdownSelector;
  degreeNameInputAndFullDisplay: typeof SubcomponentTextInput;
  educationalInstitutionInput: typeof SubcomponentTextInput;
  locationInput: typeof SubcomponentIconWithText;
  iconSpot3: "svg";
  eduInfoBar: "div";
  graduationDatesDisplay: typeof SubcomponentIconWithText;
  iconSpot4: "svg";
  startDateInput: typeof SubcomponentIconWithText;
  iconSpot5: "svg";
  endDateInput: typeof SubcomponentIconWithText;
  iconSpot6: "svg";
  graduatedInput: typeof SubcomponentCheckbox;
  subDeleteButton: typeof SubcomponentDeleteButton;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileTileEducationTile__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileTileEducationTile__VariantsArgs;
    args?: PlasmicProfileTileEducationTile__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicProfileTileEducationTile__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicProfileTileEducationTile__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicProfileTileEducationTile__ArgProps,
          internalVariantPropNames:
            PlasmicProfileTileEducationTile__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileTileEducationTile__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "educationSpacingContainer") {
    func.displayName = "PlasmicProfileTileEducationTile";
  } else {
    func.displayName = `PlasmicProfileTileEducationTile.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileTileEducationTile = Object.assign(
  // Top-level PlasmicProfileTileEducationTile renders the root element
  makeNodeComponent("educationSpacingContainer"),
  {
    // Helper components rendering sub-elements
    schoolIconDisplay: makeNodeComponent("schoolIconDisplay"),
    inforationFormatting: makeNodeComponent("inforationFormatting"),
    titleContainer: makeNodeComponent("titleContainer"),
    degreeNameContainer: makeNodeComponent("degreeNameContainer"),
    subcomponentDropdownSelector: makeNodeComponent(
      "subcomponentDropdownSelector"
    ),
    degreeNameInputAndFullDisplay: makeNodeComponent(
      "degreeNameInputAndFullDisplay"
    ),
    educationalInstitutionInput: makeNodeComponent(
      "educationalInstitutionInput"
    ),
    locationInput: makeNodeComponent("locationInput"),
    iconSpot3: makeNodeComponent("iconSpot3"),
    eduInfoBar: makeNodeComponent("eduInfoBar"),
    graduationDatesDisplay: makeNodeComponent("graduationDatesDisplay"),
    iconSpot4: makeNodeComponent("iconSpot4"),
    startDateInput: makeNodeComponent("startDateInput"),
    iconSpot5: makeNodeComponent("iconSpot5"),
    endDateInput: makeNodeComponent("endDateInput"),
    iconSpot6: makeNodeComponent("iconSpot6"),
    graduatedInput: makeNodeComponent("graduatedInput"),
    subDeleteButton: makeNodeComponent("subDeleteButton"),

    // Metadata about props expected for PlasmicProfileTileEducationTile
    internalVariantProps: PlasmicProfileTileEducationTile__VariantProps,
    internalArgProps: PlasmicProfileTileEducationTile__ArgProps
  }
);

export default PlasmicProfileTileEducationTile;
/* prettier-ignore-end */
