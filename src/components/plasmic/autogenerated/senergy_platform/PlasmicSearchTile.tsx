/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: oCayNd_9ZCYX

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import SearchCoreBookmarkButton from "../../SearchCoreBookmarkButton"; // plasmic-import: FSzKF-1XfOQy/component
import SubcomponentChevrons from "../../SubcomponentChevrons"; // plasmic-import: MDX1x1PMdyNM/component
import SubcomponentButton from "../../SubcomponentButton"; // plasmic-import: ezhuRZvm_fH9/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicSearchTile.module.css"; // plasmic-import: oCayNd_9ZCYX/css

import InfoIcon from "./icons/PlasmicIcon__Info"; // plasmic-import: 3DUdjU3Or2Rw/icon

createPlasmicElementProxy;

export type PlasmicSearchTile__VariantMembers = {
  caseStudySlides: "scrollingButtons";
  bookmark: "bookmarkFormat" | "hover";
};
export type PlasmicSearchTile__VariantsArgs = {
  caseStudySlides?: SingleChoiceArg<"scrollingButtons">;
  bookmark?: SingleChoiceArg<"bookmarkFormat" | "hover">;
};
type VariantPropType = keyof PlasmicSearchTile__VariantsArgs;
export const PlasmicSearchTile__VariantProps = new Array<VariantPropType>(
  "caseStudySlides",
  "bookmark"
);

export type PlasmicSearchTile__ArgsType = {};
type ArgPropType = keyof PlasmicSearchTile__ArgsType;
export const PlasmicSearchTile__ArgProps = new Array<ArgPropType>();

export type PlasmicSearchTile__OverridesType = {
  formattingAndShadow?: Flex__<"div">;
  headingBar?: Flex__<"div">;
  img?: Flex__<typeof PlasmicImg__>;
  freeBox?: Flex__<"div">;
  searchCoreBookmarkButton?: Flex__<typeof SearchCoreBookmarkButton>;
  imageSection?: Flex__<"section">;
  chevronStack?: Flex__<"div">;
  profileImage?: Flex__<typeof PlasmicImg__>;
  buttonSection?: Flex__<"section">;
  subButton?: Flex__<typeof SubcomponentButton>;
  subButton2?: Flex__<typeof SubcomponentButton>;
  shortDescription?: Flex__<"div">;
  section?: Flex__<"section">;
};

export interface DefaultSearchTileProps {
  caseStudySlides?: SingleChoiceArg<"scrollingButtons">;
  bookmark?: SingleChoiceArg<"bookmarkFormat" | "hover">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicSearchTile__RenderFunc(props: {
  variants: PlasmicSearchTile__VariantsArgs;
  args: PlasmicSearchTile__ArgsType;
  overrides: PlasmicSearchTile__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "caseStudySlides",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.caseStudySlides
      },
      {
        path: "bookmark",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.bookmark
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  const [isFormattingAndShadowHover, triggerFormattingAndShadowHoverProps] =
    useTrigger("useHover", {});
  const triggers = {
    hover_formattingAndShadow: isFormattingAndShadowHover
  };

  return (
    <div
      data-plasmic-name={"formattingAndShadow"}
      data-plasmic-override={overrides.formattingAndShadow}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.formattingAndShadow,
        {
          [sty.formattingAndShadowbookmark_bookmarkFormat]: hasVariant(
            $state,
            "bookmark",
            "bookmarkFormat"
          ),
          [sty.formattingAndShadowbookmark_hover]: hasVariant(
            $state,
            "bookmark",
            "hover"
          ),
          [sty.formattingAndShadowcaseStudySlides_scrollingButtons]: hasVariant(
            $state,
            "caseStudySlides",
            "scrollingButtons"
          )
        }
      )}
      data-plasmic-trigger-props={[triggerFormattingAndShadowHoverProps]}
    >
      <div
        data-plasmic-name={"headingBar"}
        data-plasmic-override={overrides.headingBar}
        className={classNames(projectcss.all, sty.headingBar, {
          [sty.headingBarbookmark_bookmarkFormat]: hasVariant(
            $state,
            "bookmark",
            "bookmarkFormat"
          ),
          [sty.headingBarbookmark_hover]: hasVariant(
            $state,
            "bookmark",
            "hover"
          ),
          [sty.headingBarcaseStudySlides_scrollingButtons]: hasVariant(
            $state,
            "caseStudySlides",
            "scrollingButtons"
          )
        })}
      >
        <PlasmicImg__
          data-plasmic-name={"img"}
          data-plasmic-override={overrides.img}
          alt={""}
          className={classNames(sty.img, {
            [sty.imgbookmark_bookmarkFormat]: hasVariant(
              $state,
              "bookmark",
              "bookmarkFormat"
            ),
            [sty.imgbookmark_hover]: hasVariant($state, "bookmark", "hover")
          })}
          displayHeight={"30px"}
          displayMaxHeight={"none"}
          displayMaxWidth={"100%"}
          displayMinHeight={"0"}
          displayMinWidth={"0"}
          displayWidth={"30px"}
          loading={"lazy"}
          src={{
            src: "/plasmic/senergy_platform/images/imagePlaceholderPng.png",
            fullWidth: 800,
            fullHeight: 600,
            aspectRatio: undefined
          }}
        />

        <div
          data-plasmic-name={"freeBox"}
          data-plasmic-override={overrides.freeBox}
          className={classNames(projectcss.all, sty.freeBox, {
            [sty.freeBoxbookmark_bookmarkFormat]: hasVariant(
              $state,
              "bookmark",
              "bookmarkFormat"
            ),
            [sty.freeBoxbookmark_hover]: hasVariant($state, "bookmark", "hover")
          })}
        >
          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__aPyHg,
              {
                [sty.textbookmark_bookmarkFormat__aPyHgCaqmZ]: hasVariant(
                  $state,
                  "bookmark",
                  "bookmarkFormat"
                ),
                [sty.textbookmark_hover__aPyHgW9E5V]: hasVariant(
                  $state,
                  "bookmark",
                  "hover"
                ),
                [sty.textcaseStudySlides_scrollingButtons__aPyHgkN1F]:
                  hasVariant($state, "caseStudySlides", "scrollingButtons")
              }
            )}
          >
            {hasVariant($state, "bookmark", "hover")
              ? "Firstname Lastname"
              : hasVariant($state, "bookmark", "bookmarkFormat")
              ? "Firstname Lastname"
              : "Firstname Lastname"}
          </div>
          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__r77Sr,
              {
                [sty.textbookmark_bookmarkFormat__r77SrCaqmZ]: hasVariant(
                  $state,
                  "bookmark",
                  "bookmarkFormat"
                ),
                [sty.textbookmark_hover__r77SrW9E5V]: hasVariant(
                  $state,
                  "bookmark",
                  "hover"
                ),
                [sty.textcaseStudySlides_scrollingButtons__r77SrkN1F]:
                  hasVariant($state, "caseStudySlides", "scrollingButtons")
              }
            )}
          >
            {"Career Title"}
          </div>
        </div>
        <SearchCoreBookmarkButton
          data-plasmic-name={"searchCoreBookmarkButton"}
          data-plasmic-override={overrides.searchCoreBookmarkButton}
          className={classNames(
            "__wab_instance",
            sty.searchCoreBookmarkButton,
            {
              [sty.searchCoreBookmarkButtonbookmark_bookmarkFormat]: hasVariant(
                $state,
                "bookmark",
                "bookmarkFormat"
              ),
              [sty.searchCoreBookmarkButtonbookmark_hover]: hasVariant(
                $state,
                "bookmark",
                "hover"
              )
            }
          )}
          selected={
            hasVariant($state, "bookmark", "hover")
              ? true
              : hasVariant($state, "bookmark", "bookmarkFormat")
              ? true
              : undefined
          }
        />
      </div>
      <section
        data-plasmic-name={"imageSection"}
        data-plasmic-override={overrides.imageSection}
        className={classNames(projectcss.all, sty.imageSection, {
          [sty.imageSectionbookmark_bookmarkFormat]: hasVariant(
            $state,
            "bookmark",
            "bookmarkFormat"
          ),
          [sty.imageSectionbookmark_hover]: hasVariant(
            $state,
            "bookmark",
            "hover"
          ),
          [sty.imageSectioncaseStudySlides_scrollingButtons]: hasVariant(
            $state,
            "caseStudySlides",
            "scrollingButtons"
          )
        })}
      >
        <Stack__
          as={"div"}
          data-plasmic-name={"chevronStack"}
          data-plasmic-override={overrides.chevronStack}
          hasGap={true}
          className={classNames(projectcss.all, sty.chevronStack, {
            [sty.chevronStackcaseStudySlides_scrollingButtons]: hasVariant(
              $state,
              "caseStudySlides",
              "scrollingButtons"
            )
          })}
        >
          <PlasmicImg__
            data-plasmic-name={"profileImage"}
            data-plasmic-override={overrides.profileImage}
            alt={""}
            className={classNames(sty.profileImage, {
              [sty.profileImagecaseStudySlides_scrollingButtons]: hasVariant(
                $state,
                "caseStudySlides",
                "scrollingButtons"
              )
            })}
            displayHeight={"190px"}
            displayMaxHeight={"none"}
            displayMaxWidth={
              hasVariant($state, "caseStudySlides", "scrollingButtons")
                ? "323px"
                : "100%"
            }
            displayMinHeight={"0"}
            displayMinWidth={"0"}
            displayWidth={
              hasVariant($state, "caseStudySlides", "scrollingButtons")
                ? "auto"
                : "325px"
            }
            loading={"lazy"}
            src={
              hasVariant($state, "caseStudySlides", "scrollingButtons")
                ? {
                    src: "/plasmic/senergy_platform/images/img0500Jpeg.jpg",
                    fullWidth: 4032,
                    fullHeight: 2268,
                    aspectRatio: undefined
                  }
                : {
                    src: "/plasmic/senergy_platform/images/imagePlaceholderPng.png",
                    fullWidth: 800,
                    fullHeight: 600,
                    aspectRatio: undefined
                  }
            }
          />

          {(
            hasVariant($state, "caseStudySlides", "scrollingButtons")
              ? true
              : false
          ) ? (
            <SubcomponentChevrons
              className={classNames(
                "__wab_instance",
                sty.subcomponentChevrons__cVeP8,
                {
                  [sty.subcomponentChevronscaseStudySlides_scrollingButtons__cVeP8KN1F]:
                    hasVariant($state, "caseStudySlides", "scrollingButtons")
                }
              )}
              direction={"right"}
            />
          ) : null}
          <SubcomponentChevrons
            className={classNames(
              "__wab_instance",
              sty.subcomponentChevrons__c1Zn,
              {
                [sty.subcomponentChevronscaseStudySlides_scrollingButtons__c1ZnkN1F]:
                  hasVariant($state, "caseStudySlides", "scrollingButtons")
              }
            )}
            direction={"left"}
          />
        </Stack__>
        <section
          data-plasmic-name={"buttonSection"}
          data-plasmic-override={overrides.buttonSection}
          className={classNames(projectcss.all, sty.buttonSection, {
            [sty.buttonSectionbookmark_bookmarkFormat]: hasVariant(
              $state,
              "bookmark",
              "bookmarkFormat"
            ),
            [sty.buttonSectionbookmark_hover]: hasVariant(
              $state,
              "bookmark",
              "hover"
            ),
            [sty.buttonSectioncaseStudySlides_scrollingButtons]: hasVariant(
              $state,
              "caseStudySlides",
              "scrollingButtons"
            )
          })}
        >
          <SubcomponentButton
            data-plasmic-name={"subButton"}
            data-plasmic-override={overrides.subButton}
            className={classNames("__wab_instance", sty.subButton, {
              [sty.subButtoncaseStudySlides_scrollingButtons]: hasVariant(
                $state,
                "caseStudySlides",
                "scrollingButtons"
              )
            })}
            endIcon={
              <svg
                className={classNames(projectcss.all, sty.svg__dBfZx)}
                role={"img"}
              />
            }
            size={triggers.hover_formattingAndShadow ? "minimal" : "minimal"}
            startIcon={
              <svg
                className={classNames(projectcss.all, sty.svg__sObm9)}
                role={"img"}
              />
            }
            styling={["nittiWColor"]}
          >
            {"Case Study"}
          </SubcomponentButton>
          <SubcomponentButton
            data-plasmic-name={"subButton2"}
            data-plasmic-override={overrides.subButton2}
            className={classNames("__wab_instance", sty.subButton2, {
              [sty.subButton2caseStudySlides_scrollingButtons]: hasVariant(
                $state,
                "caseStudySlides",
                "scrollingButtons"
              )
            })}
            endIcon={
              <svg
                className={classNames(projectcss.all, sty.svg__vHwsJ)}
                role={"img"}
              />
            }
            size={triggers.hover_formattingAndShadow ? "minimal" : "minimal"}
            startIcon={
              <svg
                className={classNames(projectcss.all, sty.svg__b3CEo)}
                role={"img"}
              />
            }
            styling={["nittiWColor"]}
          >
            {"Case Study"}
          </SubcomponentButton>
        </section>
      </section>
      <div
        data-plasmic-name={"shortDescription"}
        data-plasmic-override={overrides.shortDescription}
        className={classNames(
          projectcss.all,
          projectcss.__wab_text,
          sty.shortDescription,
          {
            [sty.shortDescriptionbookmark_bookmarkFormat]: hasVariant(
              $state,
              "bookmark",
              "bookmarkFormat"
            ),
            [sty.shortDescriptionbookmark_hover]: hasVariant(
              $state,
              "bookmark",
              "hover"
            ),
            [sty.shortDescriptioncaseStudySlides_scrollingButtons]: hasVariant(
              $state,
              "caseStudySlides",
              "scrollingButtons"
            )
          }
        )}
      >
        {
          "Deserunt occaecat aliquip quis cupidatat ea excepteur Lorem nostrud.Deserunt occaecat aliquip quis cupidatat ea excepteur Lorem nostrud.Deserunt occaecat aliquip..."
        }
      </div>
      <Stack__
        as={"section"}
        data-plasmic-name={"section"}
        data-plasmic-override={overrides.section}
        hasGap={true}
        className={classNames(projectcss.all, sty.section, {
          [sty.sectionbookmark_bookmarkFormat]: hasVariant(
            $state,
            "bookmark",
            "bookmarkFormat"
          ),
          [sty.sectionbookmark_hover]: hasVariant($state, "bookmark", "hover"),
          [sty.sectioncaseStudySlides_scrollingButtons]: hasVariant(
            $state,
            "caseStudySlides",
            "scrollingButtons"
          )
        })}
      >
        {(_par => (!_par ? [] : Array.isArray(_par) ? _par : [_par]))([
          2, 3, 4
        ]).map((__plasmic_item_0, __plasmic_idx_0) => {
          const currentItem = __plasmic_item_0;
          const currentIndex = __plasmic_idx_0;
          return (
            <InfoIcon
              className={classNames(projectcss.all, sty.svg__wdxlw, {
                [sty.svgcaseStudySlides_scrollingButtons__wdxlWkN1F]:
                  hasVariant($state, "caseStudySlides", "scrollingButtons")
              })}
              key={currentIndex}
              role={"img"}
            />
          );
        })}
      </Stack__>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  formattingAndShadow: [
    "formattingAndShadow",
    "headingBar",
    "img",
    "freeBox",
    "searchCoreBookmarkButton",
    "imageSection",
    "chevronStack",
    "profileImage",
    "buttonSection",
    "subButton",
    "subButton2",
    "shortDescription",
    "section"
  ],
  headingBar: ["headingBar", "img", "freeBox", "searchCoreBookmarkButton"],
  img: ["img"],
  freeBox: ["freeBox"],
  searchCoreBookmarkButton: ["searchCoreBookmarkButton"],
  imageSection: [
    "imageSection",
    "chevronStack",
    "profileImage",
    "buttonSection",
    "subButton",
    "subButton2"
  ],
  chevronStack: ["chevronStack", "profileImage"],
  profileImage: ["profileImage"],
  buttonSection: ["buttonSection", "subButton", "subButton2"],
  subButton: ["subButton"],
  subButton2: ["subButton2"],
  shortDescription: ["shortDescription"],
  section: ["section"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  formattingAndShadow: "div";
  headingBar: "div";
  img: typeof PlasmicImg__;
  freeBox: "div";
  searchCoreBookmarkButton: typeof SearchCoreBookmarkButton;
  imageSection: "section";
  chevronStack: "div";
  profileImage: typeof PlasmicImg__;
  buttonSection: "section";
  subButton: typeof SubcomponentButton;
  subButton2: typeof SubcomponentButton;
  shortDescription: "div";
  section: "section";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicSearchTile__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicSearchTile__VariantsArgs;
    args?: PlasmicSearchTile__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicSearchTile__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicSearchTile__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicSearchTile__ArgProps,
          internalVariantPropNames: PlasmicSearchTile__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicSearchTile__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "formattingAndShadow") {
    func.displayName = "PlasmicSearchTile";
  } else {
    func.displayName = `PlasmicSearchTile.${nodeName}`;
  }
  return func;
}

export const PlasmicSearchTile = Object.assign(
  // Top-level PlasmicSearchTile renders the root element
  makeNodeComponent("formattingAndShadow"),
  {
    // Helper components rendering sub-elements
    headingBar: makeNodeComponent("headingBar"),
    img: makeNodeComponent("img"),
    freeBox: makeNodeComponent("freeBox"),
    searchCoreBookmarkButton: makeNodeComponent("searchCoreBookmarkButton"),
    imageSection: makeNodeComponent("imageSection"),
    chevronStack: makeNodeComponent("chevronStack"),
    profileImage: makeNodeComponent("profileImage"),
    buttonSection: makeNodeComponent("buttonSection"),
    subButton: makeNodeComponent("subButton"),
    subButton2: makeNodeComponent("subButton2"),
    shortDescription: makeNodeComponent("shortDescription"),
    section: makeNodeComponent("section"),

    // Metadata about props expected for PlasmicSearchTile
    internalVariantProps: PlasmicSearchTile__VariantProps,
    internalArgProps: PlasmicSearchTile__ArgProps
  }
);

export default PlasmicSearchTile;
/* prettier-ignore-end */
